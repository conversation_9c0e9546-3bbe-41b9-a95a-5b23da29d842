import { z } from 'zod';
import { CustomerSchema } from '~~/_backend/customers/types';
import { PaginatedItemsSchema } from '~~/_backend/types';

/* ---------------------------------------------------------------------------------------------- */

export const PawnsStatsSchema = z.object({
   count: z.number(),
   created_total: z.number(),
   active: z.number(),
   collected: z.number(),
   expired: z.number(),
   active_total: z.number(),
   collected_total: z.number(),
   amount_total: z.number(),
   expired_total: z.number(),
});

export type PawnStats = z.infer<typeof PawnsStatsSchema>;

/* ---------------------------------------------------------------------------------------------- */

export const PawnSchema = z.object({
   id: z.number(),
   customer_id: z.union([z.string(), z.number()]),
   pawn_item: z.string(),
   karat: z.string(),
   interest: z.number(),
   weight: z.number(),
   value: z.number(),
   amount: z.number(),
   discount: z.number(),
   collected_amount: z.number().nullable(),
   pawn_date: z.string(),
   pledge_date: z.string(),
   collected_date: z.string().nullable(),
   expired_date: z.string().nullable(),
   state: z.enum(['ACTIVE', 'COLLECTED', 'EXPIRED']),
   interest_amount: z.number(),
   customer: CustomerSchema,
});

export const PaginatedPawnsSchema = PaginatedItemsSchema.extend({
   data: z.array(PawnSchema),
   total_value: z.number(),
   total_amount: z.number(),
   total_collected: z.number(),
   total_weight: z.number(),
});

/* ---------------------------------------------------------------------------------------------- */

// Simple paginated schema without the additional total fields
export const SimplePaginatedPawnsSchema = PaginatedItemsSchema.extend({
   data: z.array(PawnSchema),
});

export type Pawn = z.infer<typeof PawnSchema>;
export type PaginatedPawns = z.infer<typeof PaginatedPawnsSchema>;
export type SimplePaginatedPawns = z.infer<typeof SimplePaginatedPawnsSchema>;
